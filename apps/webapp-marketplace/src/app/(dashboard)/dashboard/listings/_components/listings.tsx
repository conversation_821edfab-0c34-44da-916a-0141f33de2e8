'use client';

import { useRouter } from 'next/navigation';
import { useState, useTransition } from 'react';

import {
  AlertCircle,
  CheckCircle,
  Edit,
  Eye,
  Loader2,
  Plus,
  Tag,
  Trash2,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

import { Listing, ListingStatus } from '@package/db/core/models/listing.model';
import { deleteListingById } from '../../_actions';

// Status badge styling helper
function getStatusBadgeVariant(status: ListingStatus) {
  switch (status) {
    case ListingStatus.ACTIVE:
      return 'bg-green-100 text-green-800 hover:bg-green-200';
    case ListingStatus.DRAFT:
      return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
    case ListingStatus.SOLD:
      return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
    default:
      return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
  }
}

// Format currency helper
function formatPrice(price: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(price);
}

// Format date helper
function formatDate(dateString?: string): string {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

interface ListingsProps {
  listings: Listing[];
  userId: string;
  searchParams: {
    page?: string;
    status?: string;
  };
}

export default function Listings({
  listings,
  userId,
  searchParams,
}: ListingsProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // State for delete confirmation dialog
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [listingToDelete, setListingToDelete] = useState<Listing | null>(null);

  // Filter and pagination logic (now client-side)
  const currentPage = Number(searchParams.page) || 1;
  const pageSize = 10;
  const statusFilter = searchParams.status || 'all';

  // Apply status filtering
  const filteredListings =
    statusFilter === 'all'
      ? listings
      : listings.filter((listing) => listing.status === statusFilter);

  // Apply pagination
  const totalItems = filteredListings.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const paginatedListings = filteredListings.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  // Handle status filtering
  const handleStatusChange = (value: string) => {
    const url = new URL(window.location.href);
    url.searchParams.set('status', value);
    url.searchParams.set('page', '1');
    router.push(url.toString());
  };

  const confirmDelete = (listing: Listing) => {
    setListingToDelete(listing);
    setIsDeleteDialogOpen(true);
  };

  const handleDelete = async () => {
    if (!listingToDelete) return;

    setErrorMessage(null);
    setSuccessMessage(null);

    startTransition(async () => {
      try {
        const result = await deleteListingById(listingToDelete.listingId);

        if (result.error) {
          setErrorMessage(result.error);
        } else {
          setSuccessMessage(
            `"${listingToDelete.name}" has been deleted successfully.`
          );
          // Close the dialog
          setIsDeleteDialogOpen(false);
          // Refresh the page to update the list
          setTimeout(() => {
            router.refresh();
          }, 1500);
        }
      } catch (error) {
        console.error('Error deleting listing:', error);
        setErrorMessage('Failed to delete listing. Please try again.');
      }
    });
  };

  return (
    <div className="container py-6 mx-auto space-y-6">
      {errorMessage && (
        <Alert
          variant="destructive"
          className="duration-300 border-red-500 animate-in fade-in slide-in-from-top-5 bg-red-50 dark:bg-red-900/20"
        >
          <AlertCircle className="w-4 h-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
      )}

      {successMessage && (
        <Alert className="duration-300 border-green-500 animate-in fade-in slide-in-from-top-5 bg-green-50 dark:bg-green-900/20">
          <CheckCircle className="w-4 h-4" />
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>{successMessage}</AlertDescription>
        </Alert>
      )}

      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">My Listings</h1>
        <Button asChild>
          <Link href="/dashboard/listings/create">
            <Plus className="w-4 h-4 mr-2" /> Create Listing
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div>
              <CardTitle>Manage Your Listings</CardTitle>
              <CardDescription>
                You have {totalItems}{' '}
                {totalItems === 1 ? 'listing' : 'listings'} in total
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Select
                defaultValue={statusFilter}
                onValueChange={handleStatusChange}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value={ListingStatus.ACTIVE}>Active</SelectItem>
                  <SelectItem value={ListingStatus.DRAFT}>Draft</SelectItem>
                  <SelectItem value={ListingStatus.SOLD}>Sold</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Rest of component remains the same */}
          {paginatedListings.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              {/* Empty state remains the same */}
              <div className="p-3 mb-4 rounded-full bg-primary/10">
                <Tag className="w-6 h-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold">No listings found</h3>
              <p className="mt-1 mb-4 text-sm text-muted-foreground">
                {statusFilter === 'all'
                  ? "You haven't created any listings yet."
                  : `You don't have any ${statusFilter.toLowerCase()} listings.`}
              </p>
              <Button asChild>
                <Link href="/dashboard/listings/create">
                  <Plus className="w-4 h-4 mr-2" /> Create Your First Listing
                </Link>
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              {/* Table remains the same */}
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Listing</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead className="hidden sm:table-cell">
                      Status
                    </TableHead>
                    <TableHead className="hidden md:table-cell">
                      Created
                    </TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedListings.map((listing) => (
                    <TableRow key={listing.listingId}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-3">
                          <div className="flex-shrink-0 w-10 h-10 overflow-hidden rounded-md bg-muted">
                            {listing.images && listing.images[0] ? (
                              <Image
                                src={listing.images[0].url}
                                alt={listing.name}
                                width={40}
                                height={40}
                                className="object-cover w-full h-full"
                              />
                            ) : (
                              <div className="flex items-center justify-center w-full h-full bg-secondary/20">
                                <Tag className="w-4 h-4 opacity-50" />
                              </div>
                            )}
                          </div>
                          <div className="truncate max-w-[180px] md:max-w-xs">
                            {listing.name}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{formatPrice(listing.price)}</TableCell>
                      <TableCell className="hidden sm:table-cell">
                        <Badge
                          variant="outline"
                          className={getStatusBadgeVariant(listing.status)}
                        >
                          {listing.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {formatDate(listing.createdAt?.toString())}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button size="sm" variant="ghost" asChild>
                            <Link
                              href={`/marketplace/item/${listing.listingId}/details`}
                            >
                              <Eye className="w-4 h-4" />
                              <span className="sr-only">View</span>
                            </Link>
                          </Button>
                          <Button size="sm" variant="ghost" asChild>
                            <Link
                              href={`/dashboard/listings/edit/${listing.listingId}`}
                            >
                              <Edit className="w-4 h-4" />
                              <span className="sr-only">Edit</span>
                            </Link>
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="text-red-500 hover:text-red-600"
                            onClick={() => confirmDelete(listing)}
                            disabled={isPending}
                          >
                            <Trash2 className="w-4 h-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination logic modified to use router.push instead of direct URL manipulation */}
              {totalPages > 1 && (
                <div className="flex justify-center mt-4">
                  <Pagination>
                    <PaginationContent>
                      {currentPage > 1 && (
                        <PaginationItem>
                          <PaginationPrevious
                            href={`/dashboard/listings?page=${currentPage - 1}${
                              statusFilter !== 'all'
                                ? `&status=${statusFilter}`
                                : ''
                            }`}
                          />
                        </PaginationItem>
                      )}

                      {/* Pagination items remain the same */}
                      {Array.from({ length: totalPages }).map((_, i) => {
                        const page = i + 1;
                        if (
                          page === 1 ||
                          page === totalPages ||
                          (page >= currentPage - 1 && page <= currentPage + 1)
                        ) {
                          return (
                            <PaginationItem key={page}>
                              <PaginationLink
                                href={`/dashboard/listings?page=${page}${
                                  statusFilter !== 'all'
                                    ? `&status=${statusFilter}`
                                    : ''
                                }`}
                                isActive={page === currentPage}
                              >
                                {page}
                              </PaginationLink>
                            </PaginationItem>
                          );
                        }

                        if (page === 2 || page === totalPages - 1) {
                          return (
                            <PaginationItem key={`ellipsis-${page}`}>
                              <PaginationEllipsis />
                            </PaginationItem>
                          );
                        }

                        return null;
                      })}

                      {currentPage < totalPages && (
                        <PaginationItem>
                          <PaginationNext
                            href={`/dashboard/listings?page=${currentPage + 1}${
                              statusFilter !== 'all'
                                ? `&status=${statusFilter}`
                                : ''
                            }`}
                          />
                        </PaginationItem>
                      )}
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{listingToDelete?.name}"? This
              action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          {errorMessage && (
            <Alert variant="destructive">
              <AlertCircle className="w-4 h-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{errorMessage}</AlertDescription>
            </Alert>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isPending}
            >
              {isPending ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete Listing'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
