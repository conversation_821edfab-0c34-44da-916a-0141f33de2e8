{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@/components/*": ["packages/shadcn-ui/src/components/*"], "@/components/lib/utils": ["packages/shadcn-utils/src/utils"], "@/lib/*": ["packages/shadcn-utils/src/*"], "@/lib/utils": ["packages/shadcn-utils/src/utils"], "@package/common-utils": ["packages/common-utils/src/index.ts"], "@package/configs": ["packages/configs/src/index.ts"], "@package/db": ["packages/db/src/index.ts"], "@package/db/*": ["packages/db/src/*"], "@package/mailer": ["packages/mailer/src/index.ts"], "@package/s3": ["packages/s3/src/index.ts"], "@package/s3/*": ["packages/s3/src/*"], "@package/security": ["packages/security/src/index.ts"], "@package/security/*": ["packages/security/src/*"]}}, "exclude": ["node_modules", "tmp"]}